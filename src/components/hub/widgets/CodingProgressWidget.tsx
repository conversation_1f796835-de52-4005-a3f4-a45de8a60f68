import React, { useState, use<PERSON>emo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Clock,
  Code2,
  Target,
  Calendar,
  BarChart3,
  Activity,
  Flame,
  RefreshCw,
  Trophy,
  Timer,
  Brain,
  GitBranch
} from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { cn } from '@/lib/utils';
import { useCodingProgress, type CodingProgressData, type Achievement } from '@/hooks/useCodingProgress';
import type { WidgetProps } from '@/types/hub';
import {
  <PERSON><PERSON><PERSON>,
  Area,
  <PERSON><PERSON><PERSON> as Recharts<PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>sponsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>tesianGrid,
  <PERSON><PERSON><PERSON>,
  <PERSON>
} from 'recharts';

interface CodingProgressWidgetProps extends WidgetProps {}

// Metric Card Component
const MetricCard: React.FC<{
  title: string;
  value: string | number;
  subtitle?: string;
  icon: React.ReactNode;
  trend?: 'up' | 'down' | 'neutral';
  trendValue?: string;
  color?: string;
  onClick?: () => void;
}> = ({ title, value, subtitle, icon, trend, trendValue, color = 'blue', onClick }) => {
  const trendIcon = trend === 'up' ? '↗' : trend === 'down' ? '↘' : '→';
  const trendColor = trend === 'up' ? 'text-green-500' : trend === 'down' ? 'text-red-500' : 'text-gray-500';

  return (
    <Card 
      className={cn(
        "cursor-pointer transition-all duration-200 hover:shadow-md hover:scale-105",
        onClick && "hover:bg-accent/50"
      )}
      onClick={onClick}
    >
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className={cn("p-2 rounded-lg", `bg-${color}-100 text-${color}-600`)}>
              {icon}
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">{title}</p>
              <p className="text-2xl font-bold">{value}</p>
              {subtitle && <p className="text-xs text-muted-foreground">{subtitle}</p>}
            </div>
          </div>
          {trend && trendValue && (
            <div className={cn("text-sm font-medium", trendColor)}>
              {trendIcon} {trendValue}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

// Achievement Badge Component
const AchievementBadge: React.FC<{
  achievement: Achievement;
  size?: 'sm' | 'md' | 'lg';
}> = ({ achievement, size = 'md' }) => {
  const sizeClasses = {
    sm: 'text-lg p-1',
    md: 'text-xl p-2',
    lg: 'text-2xl p-3'
  };
  
  const rarityColors = {
    common: 'border-gray-300 bg-gray-50',
    rare: 'border-blue-300 bg-blue-50',
    epic: 'border-purple-300 bg-purple-50',
    legendary: 'border-gold-300 bg-yellow-50'
  };

  return (
    <div
      className={cn(
        "inline-flex items-center gap-2 rounded-lg border-2 transition-all duration-200 hover:scale-105",
        rarityColors[achievement.rarity],
        sizeClasses[size]
      )}
      title={achievement.description}
    >
      <span className="text-lg">{achievement.icon}</span>
      {size !== 'sm' && (
        <div>
          <p className="text-sm font-medium">{achievement.title}</p>
          {size === 'lg' && (
            <p className="text-xs text-muted-foreground">{achievement.description}</p>
          )}
        </div>
      )}
    </div>
  );
};

// Language Breakdown Component
const LanguageBreakdown: React.FC<{
  languages: CodingProgressData['codeStats']['languageBreakdown'];
  compact?: boolean;
}> = ({ languages, compact = false }) => {
  const chartData = languages.slice(0, compact ? 5 : 10).map(lang => ({
    name: lang.language,
    value: lang.percentage,
    files: lang.files,
    color: lang.color
  }));

  const RADIAN = Math.PI / 180;
  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: any) => {
    if (percent < 5) return null; // Don't show labels for small slices
    
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text 
        x={x} 
        y={y} 
        fill="white" 
        textAnchor={x > cx ? 'start' : 'end'} 
        dominantBaseline="central"
        className="text-xs font-medium"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  if (compact) {
    return (
      <div className="space-y-2">
        {languages.slice(0, 5).map((lang) => (
          <div key={lang.language} className="flex items-center gap-2">
            <div 
              className="w-3 h-3 rounded-full" 
              style={{ backgroundColor: lang.color }}
            />
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium truncate">{lang.language}</span>
                <span className="text-xs text-muted-foreground">{lang.files} files</span>
              </div>
              <Progress value={lang.percentage} className="h-1 mt-1" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <RechartsPieChart>
            <Pie
              data={chartData}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={renderCustomizedLabel}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
            >
              {chartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip 
              formatter={(value: number, name: string) => [`${value.toFixed(1)}%`, name]}
              labelFormatter={(label) => `${label}`}
            />
          </RechartsPieChart>
        </ResponsiveContainer>
      </div>
      
      <div className="space-y-3">
        {languages.map((lang) => (
          <div key={lang.language} className="flex items-center gap-3">
            <div 
              className="w-4 h-4 rounded-full" 
              style={{ backgroundColor: lang.color }}
            />
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">{lang.language}</span>
                <span className="text-sm text-muted-foreground">
                  {lang.files} files ({lang.percentage.toFixed(1)}%)
                </span>
              </div>
              <div className="text-xs text-muted-foreground">
                {lang.extensions.join(', ')}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Activity Chart Component
const ActivityChart: React.FC<{
  data: CodingProgressData['productivity']['dailyActivity'];
  timeRange: string;
}> = ({ data }) => {
  const chartData = data.map(day => ({
    date: new Date(day.date).toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric' 
    }),
    sessions: day.sessions,
    duration: Math.round(day.duration),
    projects: day.projects,
    efficiency: Math.round(day.efficiency)
  }));

  return (
    <div className="h-64">
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis 
            dataKey="date" 
            tick={{ fontSize: 12 }}
            tickLine={false}
          />
          <YAxis 
            tick={{ fontSize: 12 }}
            tickLine={false}
          />
          <Tooltip 
            contentStyle={{
              backgroundColor: 'var(--background)',
              border: '1px solid var(--border)',
              borderRadius: '8px',
              fontSize: '12px'
            }}
            formatter={(value: number, name: string) => {
              const formatters: Record<string, (val: number) => string> = {
                duration: (val) => `${val} min`,
                sessions: (val) => `${val} sessions`,
                projects: (val) => `${val} projects`,
                efficiency: (val) => `${val} min/session`
              };
              return [formatters[name]?.(value) || value, name];
            }}
          />
          <Legend />
          <Area 
            type="monotone" 
            dataKey="duration" 
            stackId="1"
            stroke="#3b82f6" 
            fill="#3b82f6" 
            fillOpacity={0.6}
            name="Duration"
          />
          <Area 
            type="monotone" 
            dataKey="sessions" 
            stackId="2"
            stroke="#10b981" 
            fill="#10b981" 
            fillOpacity={0.6}
            name="Sessions"
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );
};

// Goals Section Component
const GoalsSection: React.FC<{
  goals: CodingProgressData['goals'];
  onUpdateGoals: (goals: Partial<CodingProgressData['goals']>) => void;
}> = ({ goals }) => {

  const goalItems = [
    {
      key: 'daily',
      title: 'Daily Goal',
      target: goals.dailyGoal,
      progress: goals.dailyProgress,
      unit: 'minutes',
      icon: <Calendar className="h-4 w-4" />
    },
    {
      key: 'weekly',
      title: 'Weekly Goal',
      target: goals.weeklyGoal,
      progress: goals.weeklyProgress,
      unit: 'minutes',
      icon: <BarChart3 className="h-4 w-4" />
    },
    {
      key: 'monthly',
      title: 'Monthly Goal',
      target: goals.monthlyGoal,
      progress: goals.monthlyProgress,
      unit: 'minutes',
      icon: <Target className="h-4 w-4" />
    }
  ];

  return (
    <div className="space-y-4">
      {goalItems.map((goal) => (
        <Card key={goal.key}>
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                {goal.icon}
                <span className="font-medium">{goal.title}</span>
              </div>
              <Badge variant="outline">
                {Math.round((goal.target / 60) * 10) / 10}h target
              </Badge>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Progress</span>
                <span>{Math.round(goal.progress)}%</span>
              </div>
              <Progress value={goal.progress} className="h-2" />
              <div className="text-xs text-muted-foreground">
                {Math.round((goal.target * goal.progress / 100) / 60 * 10) / 10}h of{' '}
                {Math.round(goal.target / 60 * 10) / 10}h goal
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

// Main Widget Component
export const CodingProgressWidget: React.FC<CodingProgressWidgetProps> = ({
  widget,
  onUpdate,
  onRefresh
}) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [timeRange, setTimeRange] = useState<'today' | 'week' | 'month' | 'year'>('week');
  
  const widgetSettings = widget.settings as {
    timeRange?: 'today' | 'week' | 'month' | 'year';
    showGoals?: boolean;
    showLanguageBreakdown?: boolean;
    showActivityChart?: boolean;
  };

  const {
    data,
    loading,
    error,
    refreshing,
    lastUpdated,
    refresh,
    setTimeRange: updateTimeRange,
    updateGoals,
    clearError,
    totalCodingTime,
    todayProgress
  } = useCodingProgress({
    timeRange: widgetSettings.timeRange || timeRange,
    autoRefresh: true,
    refreshInterval: 15
  });

  const handleTimeRangeChange = (newRange: 'today' | 'week' | 'month' | 'year') => {
    setTimeRange(newRange);
    updateTimeRange(newRange);
    onUpdate({
      settings: {
        ...widget.settings,
        timeRange: newRange
      }
    });
  };

  const handleRefresh = async () => {
    await refresh();
    onRefresh();
  };

  // Memoized calculations
  const quickStats = useMemo(() => {
    if (!data) return [];
    
    return [
      {
        title: 'Total Sessions',
        value: data.sessionStats.totalSessions,
        subtitle: `${data.sessionStats.sessionsToday} today`,
        icon: <Clock className="h-4 w-4" />,
        trend: 'up' as const,
        trendValue: '+12%',
        color: 'blue'
      },
      {
        title: 'Coding Time',
        value: `${Math.round(totalCodingTime / 60)}h`,
        subtitle: `${Math.round(data.sessionStats.averageSessionDuration)}m avg`,
        icon: <Timer className="h-4 w-4" />,
        trend: 'up' as const,
        trendValue: '+8%',
        color: 'green'
      },
      {
        title: 'Projects',
        value: data.projectStats.totalProjects,
        subtitle: `${data.projectStats.activeProjects} active`,
        icon: <GitBranch className="h-4 w-4" />,
        trend: 'neutral' as const,
        trendValue: '→',
        color: 'purple'
      },
      {
        title: 'Languages',
        value: data.codeStats.languageBreakdown.length,
        subtitle: `${data.codeStats.totalFiles} files`,
        icon: <Code2 className="h-4 w-4" />,
        trend: 'up' as const,
        trendValue: '+2',
        color: 'orange'
      }
    ];
  }, [data, totalCodingTime]);

  if (loading && !data) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2" />
          <div className="text-sm text-muted-foreground">Loading progress data...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 mb-2">Failed to load progress data</div>
          <div className="text-sm text-muted-foreground mb-3">{error}</div>
          <Button variant="outline" size="sm" onClick={clearError}>
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center text-muted-foreground">
          No coding data available
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header Controls */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <Select value={timeRange} onValueChange={handleTimeRangeChange}>
            <SelectTrigger className="w-32 h-8 text-xs">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="today">Today</SelectItem>
              <SelectItem value="week">This Week</SelectItem>
              <SelectItem value="month">This Month</SelectItem>
              <SelectItem value="year">This Year</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={handleRefresh}
          disabled={refreshing}
          className="h-8 w-8 p-0"
        >
          {refreshing ? (
            <div className="animate-spin rounded-full h-3 w-3 border-b border-current" />
          ) : (
            <RefreshCw className="h-3 w-3" />
          )}
        </Button>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-4 mb-4">
          <TabsTrigger value="overview" className="text-xs">Overview</TabsTrigger>
          <TabsTrigger value="activity" className="text-xs">Activity</TabsTrigger>
          <TabsTrigger value="languages" className="text-xs">Languages</TabsTrigger>
          <TabsTrigger value="goals" className="text-xs">Goals</TabsTrigger>
        </TabsList>

        <div className="flex-1 overflow-auto">
          <AnimatePresence mode="wait">
            <TabsContent value="overview" className="space-y-4 mt-0">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="space-y-4"
              >
                {/* Quick Stats Grid */}
                <div className="grid grid-cols-2 gap-3">
                  {quickStats.map((stat) => (
                    <MetricCard key={stat.title} {...stat} />
                  ))}
                </div>

                {/* Today's Progress */}
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm flex items-center gap-2">
                      <Flame className="h-4 w-4 text-orange-500" />
                      Today's Progress
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between text-sm">
                        <span>Daily Goal</span>
                        <span>{Math.round(todayProgress)}%</span>
                      </div>
                      <Progress value={todayProgress} className="h-2" />
                      <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <span>
                          {Math.round((data.goals.dailyGoal * todayProgress / 100) / 60 * 10) / 10}h coded
                        </span>
                        <span>
                          {Math.round(data.goals.dailyGoal / 60 * 10) / 10}h goal
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Current Streak */}
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="p-2 rounded-lg bg-red-100 text-red-600">
                          <Flame className="h-4 w-4" />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">Current Streak</p>
                          <p className="text-2xl font-bold">{data.productivity.streaks.current}</p>
                          <p className="text-xs text-muted-foreground">
                            Best: {data.productivity.streaks.longest} days
                          </p>
                        </div>
                      </div>
                      <Badge variant="outline" className="text-orange-600 border-orange-600">
                        {data.productivity.streaks.current === data.productivity.streaks.longest ? 'Personal Best!' : 'Keep Going!'}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>

                {/* Recent Achievements */}
                {data.goals.achievements.length > 0 && (
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm flex items-center gap-2">
                        <Trophy className="h-4 w-4 text-yellow-500" />
                        Recent Achievements
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex flex-wrap gap-2">
                        {data.goals.achievements.slice(0, 3).map((achievement) => (
                          <AchievementBadge 
                            key={achievement.id} 
                            achievement={achievement} 
                            size="sm" 
                          />
                        ))}
                        {data.goals.achievements.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{data.goals.achievements.length - 3} more
                          </Badge>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </motion.div>
            </TabsContent>

            <TabsContent value="activity" className="space-y-4 mt-0">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="space-y-4"
              >
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Activity Timeline</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ActivityChart data={data.productivity.dailyActivity} timeRange={timeRange} />
                  </CardContent>
                </Card>

                {/* Peak Hours */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Peak Productivity Hours</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-3 gap-2">
                      {data.productivity.peakHours.slice(0, 6).map((hour) => (
                        <div key={hour.hour} className="text-center p-2 rounded-lg bg-accent/50">
                          <div className="text-sm font-medium">
                            {hour.hour}:00
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {hour.sessions} sessions
                          </div>
                          <div className="text-xs text-blue-600">
                            {Math.round(hour.percentage)}%
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </TabsContent>

            <TabsContent value="languages" className="space-y-4 mt-0">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="space-y-4"
              >
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Language Breakdown</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <LanguageBreakdown 
                      languages={data.codeStats.languageBreakdown} 
                      compact={false}
                    />
                  </CardContent>
                </Card>
              </motion.div>
            </TabsContent>

            <TabsContent value="goals" className="space-y-4 mt-0">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="space-y-4"
              >
                <GoalsSection goals={data.goals} onUpdateGoals={updateGoals} />
              </motion.div>
            </TabsContent>
          </AnimatePresence>
        </div>
      </Tabs>

      {/* Footer */}
      {lastUpdated && (
        <div className="mt-4 pt-3 border-t">
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center gap-1">
              <Activity className="h-3 w-3" />
              Updated {lastUpdated.toLocaleTimeString()}
            </div>
            <div className="flex items-center gap-1">
              <Brain className="h-3 w-3" />
              ${data.claudeUsage.totalCost.toFixed(2)} spent
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CodingProgressWidget;